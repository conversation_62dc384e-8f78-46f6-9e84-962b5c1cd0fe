// 检查数据输入任务
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkDataInput() {
  try {
    console.log('🔍 检查数据输入任务...')
    
    // 查找数据输入关卡
    const dataInputLevel = await prisma.level.findFirst({
      where: { name: '数据输入' },
      include: {
        tasks: {
          orderBy: { order: 'asc' }
        },
        parent: true
      }
    })
    
    if (dataInputLevel) {
      console.log('✅ 找到数据输入关卡:')
      console.log('- ID:', dataInputLevel.id)
      console.log('- 名称:', dataInputLevel.name)
      console.log('- 父级:', dataInputLevel.parent?.name)
      console.log('- 任务数量:', dataInputLevel.tasks.length)
      
      if (dataInputLevel.tasks.length > 0) {
        console.log('📋 任务列表:')
        dataInputLevel.tasks.forEach(task => {
          console.log(`  - ${task.name} (ID: ${task.id})`)
        })
      } else {
        console.log('❌ 没有找到任务')
      }
    } else {
      console.log('❌ 未找到数据输入关卡')
    }
    
    // 检查测试用户的进度
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (testUser && dataInputLevel) {
      const progress = await prisma.userProgress.findUnique({
        where: {
          userId_levelId: {
            userId: testUser.id,
            levelId: dataInputLevel.id
          }
        }
      })
      
      console.log('📊 用户进度:', progress ? '已有记录' : '无记录')
      if (progress) {
        console.log('  - 完成状态:', progress.completed)
        console.log('  - 积分:', progress.score)
        console.log('  - 尝试次数:', progress.attempts)
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDataInput()
