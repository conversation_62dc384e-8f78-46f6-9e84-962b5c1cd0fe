import { prisma } from '$lib/db'
import type { RequestHandler } from './$types'

export const GET: RequestHandler = async ({ request, url }) => {
  // 优先从代理头部获取原始域名和协议
  const host = 
    request.headers.get('x-forwarded-host') || // Vercel/AWS/Nginx 标准
    request.headers.get('host') || // 直接访问时的备用
    url.host || // SvelteKit URL对象
    'localhost:5173' // 回退值

  const protocol = 'https' // 生产环境使用https
    // request.headers.get('x-forwarded-proto') || // 标准代理协议头
    // url.protocol.replace(':', '') // 根据URL判断

  const baseUrl = `${protocol}://${host}`
  
  try {
    // 获取所有关卡和任务数据
    const levels = await prisma.level.findMany({
      include: {
        tasks: true
      },
      orderBy: {
        order: 'asc'
      }
    })

    // 生成sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- 首页 -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- 法律页面 -->
  <url>
    <loc>${baseUrl}/legal/privacy-policy</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/legal/terms-of-service</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
  
  <!-- 关卡页面 -->
  ${levels.map(level => `
  <url>
    <loc>${baseUrl}/level/${level.id}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('')}

  <!-- 任务页面 -->
  ${levels.flatMap(level =>
    level.tasks.map(task => `
  <url>
    <loc>${baseUrl}/task/${task.id}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`)
  ).join('')}
</urlset>`

    return new Response(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // 如果数据库查询失败，返回基础sitemap
    const basicSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/legal/privacy-policy</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/legal/terms-of-service</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
</urlset>`

    return new Response(basicSitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  }
}
