# Ubuntu 服务器部署指南

本文档详细介绍如何在 Ubuntu 服务器上部署 Learn-Excel-Svelte 项目。

## 系统要求

- Ubuntu 20.04 LTS 或更高版本
- 至少 2GB RAM
- 至少 10GB 可用磁盘空间
- 具有 sudo 权限的用户账户

## 1. 更新系统

```bash
sudo apt update
sudo apt upgrade -y
```

## 2. 安装 Node.js 和 pnpm

### 安装 Node.js (推荐使用 NodeSource 仓库)

```bash
# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# 安装 Node.js
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 安装 pnpm

```bash
# 使用 npm 安装 pnpm
sudo npm install -g pnpm

# 验证安装
pnpm --version
```

## 3. 安装数据库 (PostgreSQL)

```bash
# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# 启动并启用 PostgreSQL 服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库用户和数据库
sudo -u postgres psql
```

在 PostgreSQL 命令行中执行：

```sql
CREATE USER learn_excel_user WITH PASSWORD 'your_secure_password';
CREATE DATABASE learn_excel_db OWNER learn_excel_user;
GRANT ALL PRIVILEGES ON DATABASE learn_excel_db TO learn_excel_user;
\q
```

## 4. 安装 Git

```bash
sudo apt install git -y
```

## 5. 克隆项目

```bash
# 创建项目目录
sudo mkdir -p /var/www
cd /var/www

# 克隆项目 (替换为你的实际仓库地址)
sudo git clone https://github.com/your-username/Learn-Excel-Svelte.git
sudo chown -R $USER:$USER /var/www/Learn-Excel-Svelte
cd Learn-Excel-Svelte
```

## 6. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

配置以下关键环境变量：

```env
# 数据库配置
DATABASE_URL="postgresql://learn_excel_user:your_secure_password@localhost:5432/learn_excel_db"

# 认证密钥 (生成一个强密码)
AUTH_SECRET="your-very-long-random-secret-key"

# 邮件配置 (如果需要邮件功能)
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# 应用配置
ORIGIN="https://your-domain.com"
```

## 7. 安装依赖和构建项目

```bash
# 安装项目依赖
pnpm install

# 生成 Prisma 客户端
pnpm prisma generate

# 运行数据库迁移
pnpm prisma db push

# 构建项目
pnpm build
```

## 8. 安装和配置 PM2 (进程管理器)

```bash
# 安装 PM2
sudo npm install -g pm2

# 创建 PM2 配置文件
nano ecosystem.config.js
```

在 `ecosystem.config.js` 中添加：

```javascript
module.exports = {
  apps: [{
    name: 'learn-excel-svelte',
    script: 'build/index.js',
    cwd: '/var/www/Learn-Excel-Svelte',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/pm2/learn-excel-svelte-error.log',
    out_file: '/var/log/pm2/learn-excel-svelte-out.log',
    log_file: '/var/log/pm2/learn-excel-svelte.log'
  }]
}
```

```bash
# 创建日志目录
sudo mkdir -p /var/log/pm2
sudo chown -R $USER:$USER /var/log/pm2

# 启动应用
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save

# 设置 PM2 开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

## 9. 安装和配置 Nginx (反向代理)

```bash
# 安装 Nginx
sudo apt install nginx -y

# 创建站点配置
sudo nano /etc/nginx/sites-available/learn-excel-svelte
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/learn-excel-svelte /etc/nginx/sites-enabled/

# 测试 Nginx 配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 10. 配置 SSL 证书 (使用 Let's Encrypt)

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 测试自动续期
sudo certbot renew --dry-run
```

## 11. 配置防火墙

```bash
# 启用 UFW 防火墙
sudo ufw enable

# 允许必要的端口
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'

# 检查防火墙状态
sudo ufw status
```

## 12. 监控和维护

### 查看应用状态

```bash
# 查看 PM2 进程状态
pm2 status

# 查看应用日志
pm2 logs learn-excel-svelte

# 重启应用
pm2 restart learn-excel-svelte
```

### 更新应用

```bash
cd /var/www/Learn-Excel-Svelte

# 拉取最新代码
git pull origin main

# 安装新依赖 (如果有)
pnpm install

# 运行数据库迁移 (如果有)
pnpm prisma db push

# 重新构建
pnpm build

# 重启应用
pm2 restart learn-excel-svelte
```

### 备份数据库

```bash
# 创建备份脚本
sudo nano /usr/local/bin/backup-db.sh
```

添加以下内容：

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="learn_excel_db"

mkdir -p $BACKUP_DIR
pg_dump -U learn_excel_user -h localhost $DB_NAME > $BACKUP_DIR/backup_$DATE.sql
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

```bash
# 使脚本可执行
sudo chmod +x /usr/local/bin/backup-db.sh

# 添加到 crontab (每天凌晨 2 点备份)
sudo crontab -e
# 添加这一行：
# 0 2 * * * /usr/local/bin/backup-db.sh
```

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查环境变量配置
   - 查看 PM2 日志：`pm2 logs`
   - 确认数据库连接正常

2. **数据库连接失败**
   - 检查 PostgreSQL 服务状态：`sudo systemctl status postgresql`
   - 验证数据库用户权限
   - 检查 DATABASE_URL 配置

3. **Nginx 502 错误**
   - 确认应用在端口 3000 运行：`pm2 status`
   - 检查 Nginx 配置：`sudo nginx -t`
   - 查看 Nginx 错误日志：`sudo tail -f /var/log/nginx/error.log`

### 有用的命令

```bash
# 查看系统资源使用情况
htop

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h

# 查看网络连接
netstat -tulpn

# 查看系统日志
sudo journalctl -f
```

## 安全建议

1. 定期更新系统和依赖包
2. 使用强密码和密钥
3. 启用防火墙并只开放必要端口
4. 定期备份数据库
5. 监控系统资源和日志
6. 考虑使用 fail2ban 防止暴力攻击

## 结论

按照以上步骤，你应该能够成功在 Ubuntu 服务器上部署 Learn-Excel-Svelte 项目。如果遇到问题，请检查日志文件并参考故障排除部分。

记住要定期维护和更新你的服务器以确保安全性和性能。
