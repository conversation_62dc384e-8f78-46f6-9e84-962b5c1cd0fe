import { json } from '@sveltejs/kit'
import type { RequestHandler } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { getSession, destroySession } from '$lib/session'

export const DELETE: RequestHandler = async ({ cookies }) => {
  try {
    const session = await getSession(cookies)

    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { 
        email: true, 
        username: true,
        userType: true
      }
    })

    if (!user) {
      return json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 在事务中删除用户及相关数据
    await prisma.$transaction(async (tx) => {
      // 1. 删除用户的学习进度记录
      await tx.userProgress.deleteMany({
        where: { userId }
      })

      // 2. 如果用户拥有好友邀请码，删除这些邀请码
      await tx.friendInviteCode.deleteMany({
        where: { ownerId: userId }
      })

      // 3. 将用户使用过的邀请码的usedBy字段设为null（保留邀请码记录但清除使用者信息）
      await tx.betaInviteCode.updateMany({
        where: { usedBy: userId },
        data: { usedBy: null }
      })

      await tx.friendInviteCode.updateMany({
        where: { usedBy: userId },
        data: { usedBy: null }
      })

      // 4. 记录注销的邮箱（防止30天内重复注册）
      await tx.deletedAccount.create({
        data: {
          email: user.email
        }
      })

      // 5. 删除用户记录
      await tx.user.delete({
        where: { id: userId }
      })
    })

    // 记录账户删除日志
    log.info(`账户注销成功: ${user.email}, 用户名: ${user.username}`, {
      userId,
      email: user.email,
      username: user.username,
      userType: user.userType
    })

    // 创建响应并清除session相关的cookies
    const response = json(
      { message: '账户注销成功，30天内不能使用相同邮箱重新注册' },
      { status: 200 }
    )

    // 销毁自定义session
    destroySession(cookies)

    // 清除Auth.js相关的cookies
    const authCookies = [
      'authjs.session-token',
      'authjs.csrf-token',
      'authjs.callback-url',
      '__Secure-authjs.session-token',
      '__Host-authjs.csrf-token'
    ]

    authCookies.forEach(cookieName => {
      cookies.delete(cookieName, { path: '/' })
      cookies.delete(cookieName, { path: '/', domain: undefined })
    })

    return response
  } catch (error) {
    console.error('删除账户错误:', error)
    log.error('error', '删除账户失败', { error: error })
    return json(
      { error: '删除账户失败，请稍后重试' },
      { status: 500 }
    )
  }
}
