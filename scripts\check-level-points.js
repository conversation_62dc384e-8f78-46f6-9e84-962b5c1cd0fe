// 检查关卡积分设置
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkLevelPoints() {
  try {
    console.log('🔍 检查数据输入关卡积分...')
    
    // 查找数据输入关卡
    const dataInputLevel = await prisma.level.findFirst({
      where: { name: '数据输入' },
      include: {
        tasks: true,
        parent: true
      }
    })
    
    if (dataInputLevel) {
      console.log('✅ 数据输入关卡信息:')
      console.log('- ID:', dataInputLevel.id)
      console.log('- 名称:', dataInputLevel.name)
      console.log('- 积分:', dataInputLevel.points)
      console.log('- 父级:', dataInputLevel.parent?.name)
      console.log('- 任务数量:', dataInputLevel.tasks.length)
      
      if (dataInputLevel.tasks.length > 0) {
        console.log('📋 任务信息:')
        dataInputLevel.tasks.forEach(task => {
          console.log(`  - ${task.name} (ID: ${task.id}, levelId: ${task.levelId})`)
        })
      }
    } else {
      console.log('❌ 未找到数据输入关卡')
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkLevelPoints()
