import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkTestUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user) {
      console.log('Test user found:')
      console.log('Email:', user.email)
      console.log('Username:', user.username)
      console.log('ID:', user.id)
      console.log('Email verified:', user.emailVerified)
      console.log('Password hash:', user.password ? 'exists' : 'missing')
    } else {
      console.log('Test user not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkTestUser()
