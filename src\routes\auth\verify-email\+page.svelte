<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'

  let status: 'loading' | 'success' | 'error' = 'loading'
  let message = ''

  onMount(async () => {
    const token = $page.url.searchParams.get('token')

    if (!token) {
      status = 'error'
      message = '验证令牌缺失'
      return
    }

    // 调用验证API
    try {
      const response = await fetch(`/api/auth/verify-email?token=${token}`)
      const data = await response.json()

      if (response.ok) {
        status = 'success'
        message = data.message
        // 3秒后跳转到登录页面
        setTimeout(() => {
          goto('/auth/signin?message=邮箱验证成功，请登录')
        }, 3000)
      } else {
        status = 'error'
        message = data.error || '验证失败'
      }
    } catch {
      status = 'error'
      message = '网络错误，请稍后重试'
    }
  })
</script>

<svelte:head>
  <title>邮箱验证 - Excel学习平台</title>
  <meta name="description" content="验证您的邮箱地址以完成注册" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
        邮箱验证
      </h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
      {#if status === 'loading'}
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p class="text-gray-600">正在验证您的邮箱...</p>
        </div>
      {/if}

      {#if status === 'success'}
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">验证成功！</h3>
          <p class="text-gray-600 mb-4">{message}</p>
          <p class="text-sm text-gray-500">页面将在3秒后自动跳转到登录页面...</p>
          <div class="mt-4">
            <a
              href="/auth/signin"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              立即登录
            </a>
          </div>
        </div>
      {/if}

      {#if status === 'error'}
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">验证失败</h3>
          <p class="text-gray-600 mb-4">{message}</p>
          <div class="space-y-2">
            <a
              href="/auth/signup"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 mr-2"
            >
              重新注册
            </a>
            <a
              href="/signin"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              返回登录
            </a>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>
