/**
 * 检查第一轮测试中有问题的任务
 * 这些任务在第一轮测试中遇到了"未找到提交按钮"的问题
 */

import { chromium } from 'playwright';

const PROBLEMATIC_TASKS = [
  {
    name: '简单排序',
    taskId: 'cmdsdv50l001ru480cd7pwbr2',
    description: '检查简单排序任务页面'
  },
  {
    name: '复杂排序',
    taskId: 'cmdsdv50q001vu480ot2phb21',
    description: '检查复杂排序任务页面'
  },
  {
    name: '简单数据验证',
    taskId: 'cmdsdv50t001zu48011lbkwvt',
    description: '检查简单数据验证任务页面'
  },
  {
    name: '数据透视表',
    taskId: 'cmdsdv51t002tu480ai1y58eo',
    description: '检查数据透视表任务页面'
  }
];

async function checkTask(page, task) {
  try {
    console.log(`\n🔍 检查任务: ${task.name}`);

    // 导航到任务页面
    const taskUrl = `http://localhost:5173/task/${task.taskId}`;
    console.log(`🌐 导航到: ${taskUrl}`);
    await page.goto(taskUrl);
    await page.waitForLoadState('networkidle');

    // 检查页面状态
    const currentUrl = page.url();
    const pageTitle = await page.title();
    console.log(`📄 当前URL: ${currentUrl}`);
    console.log(`📝 页面标题: ${pageTitle}`);

    // 检查是否被重定向到登录页面
    if (currentUrl.includes('/login')) {
      console.log('❌ 被重定向到登录页面');
      return { success: false, message: '需要重新登录' };
    }

    // 等待页面加载完成
    try {
      await page.waitForSelector('h1', { timeout: 10000 });
      console.log('✅ 页面标题加载成功');
    } catch (error) {
      console.log('❌ 页面标题加载失败');
      return { success: false, message: '页面加载失败' };
    }

    // 检查Excel组件
    try {
      await page.waitForSelector('.univer-container', { timeout: 15000 });
      console.log('✅ Excel组件加载成功');
    } catch (error) {
      console.log('⚠️ Excel组件加载超时');
    }

    // 查找提交按钮
    const submitButtons = await page.locator('button').all();
    console.log(`🔘 页面上的按钮数量: ${submitButtons.length}`);
    
    let foundSubmitButton = false;
    for (let i = 0; i < submitButtons.length; i++) {
      const text = await submitButtons[i].textContent();
      const isVisible = await submitButtons[i].isVisible();
      const isEnabled = await submitButtons[i].isEnabled();
      console.log(`  ${i + 1}. "${text}" - 可见: ${isVisible}, 可用: ${isEnabled}`);
      
      if (text && text.includes('提交任务')) {
        foundSubmitButton = true;
        console.log('✅ 找到提交按钮');
      }
    }

    if (!foundSubmitButton) {
      console.log('❌ 未找到提交按钮');
      
      // 检查页面HTML结构
      const bodyHTML = await page.innerHTML('body');
      const hasSubmitText = bodyHTML.includes('提交任务');
      console.log(`🔍 页面HTML中包含"提交任务"文本: ${hasSubmitText}`);
      
      return { success: false, message: '未找到提交按钮' };
    }

    // 尝试点击提交按钮测试验证
    console.log('🖱️ 测试点击提交按钮...');
    const submitButton = page.locator('button:has-text("🚀 提交任务")');
    await submitButton.first().click();
    await page.waitForTimeout(3000);

    // 检查验证结果
    const pageText = await page.textContent('body');
    if (pageText.includes('闯关失败') || pageText.includes('❌')) {
      console.log(`✅ 验证逻辑正常工作 - 空操作被拒绝`);
      return { success: true, message: '验证逻辑正常，空操作被正确拒绝' };
    } else if (pageText.includes('闯关成功') || pageText.includes('✅')) {
      console.log(`⚠️ 验证逻辑可能有问题 - 空操作被接受`);
      return { success: false, message: '验证逻辑有问题，空操作被错误接受' };
    } else {
      console.log('❓ 验证结果不明确');
      return { success: false, message: '验证结果不明确' };
    }

  } catch (error) {
    console.log(`❌ 检查任务 ${task.name} 时发生错误: ${error.message}`);
    return { success: false, message: `检查错误: ${error.message}` };
  }
}

async function runCheck() {
  console.log('🔍 检查第一轮测试中有问题的任务');
  console.log('📋 目标：确定这些任务的页面加载和验证逻辑状态');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  const results = [];

  try {
    // 登录
    console.log('\n🔐 登录测试账户...');
    await page.goto('http://localhost:5173/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', '123456');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    await page.waitForLoadState('networkidle');
    
    const currentUrl = page.url();
    console.log(`🌐 登录后URL: ${currentUrl}`);
    
    if (currentUrl.includes('/login')) {
      console.log('❌ 登录失败');
      return;
    } else {
      console.log('✅ 登录成功');
    }

    // 检查每个有问题的任务
    for (const task of PROBLEMATIC_TASKS) {
      const result = await checkTask(page, task);
      results.push({
        task: task.name,
        ...result
      });
      
      await page.waitForTimeout(2000);
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  } finally {
    await browser.close();
  }

  // 输出检查结果
  console.log('\n📊 问题任务检查结果:');
  console.log('============================================================');
  
  const working = results.filter(r => r.success).length;
  const problematic = results.filter(r => !r.success).length;
  
  console.log(`✅ 正常工作: ${working}/${results.length}`);
  console.log(`❌ 仍有问题: ${problematic}/${results.length}`);

  if (problematic > 0) {
    console.log('\n❌ 仍有问题的任务:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.task}: ${r.message}`);
    });
  }

  if (working > 0) {
    console.log('\n✅ 正常工作的任务:');
    results.filter(r => r.success).forEach(r => {
      console.log(`  - ${r.task}: ${r.message}`);
    });
  }

  console.log('\n🎯 检查完成!');
  return results;
}

runCheck();
