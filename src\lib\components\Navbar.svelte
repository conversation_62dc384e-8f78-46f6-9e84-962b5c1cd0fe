<script lang="ts">
  import { page } from '$app/stores'
  import { navbarStore } from '$lib/stores/navbar'

  export let session: any = null

  $: pathname = $page.url.pathname
  $: navbarData = $navbarStore

  // 调试信息 - 在开发环境中显示
  $: if (typeof window !== 'undefined' && import.meta.env.DEV) {
    console.log('Navbar render:', { 
      showLevelList: navbarData.showLevelList, 
      levelListHref: navbarData.levelListHref, 
      pathname 
    })
  }

  const handleSignOut = async () => {
    // 调用登出API
    try {
      await fetch('/api/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      // 强制刷新页面以确保所有状态都被清除
      window.location.href = '/'
    } catch (error) {
      console.error('Logout failed:', error)
      // 如果API调用失败，直接重定向到首页
      window.location.href = '/'
    }
  }

  // 不在登录/注册页面和主页显示导航栏
  $: shouldShowNavbar = !pathname?.startsWith('/auth/') && pathname !== '/' && pathname !== '/signin' && pathname !== '/auth/signup'
</script>

{#if shouldShowNavbar}
  <nav class="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 fixed top-0 left-0 right-0 z-50">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- 左侧品牌和导航菜单 -->
        <div class="flex items-center space-x-2 sm:space-x-4 lg:space-x-8 min-w-0 flex-1">
          <!-- 品牌Logo -->
          <a
            href="/"
            class="flex items-center space-x-1 sm:space-x-2 group flex-shrink-0"
          >
            <div class="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
              <span class="text-white font-bold text-xs sm:text-sm">🏠</span>
            </div>
            <span class="text-sm sm:text-base font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent hidden sm:block">
              首页
            </span>
          </a>

          <!-- 导航菜单 -->
          {#if session}
            <div class="flex items-center space-x-1 min-w-0">
              <a
                href="/dashboard"
                class={`px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                  pathname === '/dashboard'
                    ? 'text-green-600 bg-green-50 shadow-sm'
                    : 'text-gray-600 hover:text-green-600 hover:bg-green-50/50'
                }`}
              >
                <span class="flex items-center space-x-1">
                  <span>🗺️</span>
                  <span class="hidden sm:inline">主关卡</span>
                </span>
              </a>

              <!-- 子关卡链接，只在task页面显示 -->
              {#if pathname.startsWith('/task/')}
                <a
                  href={navbarData.levelListHref || "/dashboard"}
                  class="px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium text-gray-600 hover:text-green-600 hover:bg-green-50/50 transition-all duration-200 flex-shrink-0"
                >
                  <span class="flex items-center space-x-1">
                    <span>🧩</span>
                    <span class="hidden sm:inline">子关卡</span>
                  </span>
                </a>
              {/if}
            </div>
          {/if}
        </div>

        <!-- 右侧用户信息 -->
        {#if session}
          <div class="flex items-center space-x-1 sm:space-x-2 lg:space-x-4 flex-shrink-0">
            <!-- 邀请码链接 -->
            <a
              href="/invite-codes"
              class={`px-2 sm:px-3 py-1.5 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                pathname === '/invite-codes'
                  ? 'text-purple-600 bg-purple-50 shadow-sm'
                  : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50/50'
              }`}
            >
              <span class="flex items-center space-x-1">
                <span>🎫</span>
                <span class="hidden sm:inline">邀请码</span>
              </span>
            </a>

            <!-- 经验值显示 -->
            <div class="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1.5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-100">
              <span class="text-blue-600 text-sm">⭐</span>
              <span class="text-xs sm:text-sm font-medium text-blue-700">{session.user.score || 0}</span>
              <span class="text-xs text-blue-500 hidden sm:inline">EXP</span>
            </div>

            <!-- 用户头像和名称 -->
            <div class="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1.5 bg-gray-50 rounded-full border border-gray-200">
              {#if session.user.userType === 'beta'}
                <a href="/profile" class="relative group">
                  <img
                    src="/icon.svg"
                    alt="VIP"
                    class="w-5 h-5 sm:w-6 sm:h-6 cursor-pointer hover:scale-105 transition-transform duration-200"
                  />
                  <!-- VIP悬停提示 -->
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gradient-to-br from-yellow-300 via-amber-300 to-yellow-400 text-amber-800 text-sm font-semibold rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-50 border border-amber-200 ring-1 ring-yellow-400/50">
                    <span class="text-amber-900 drop-shadow-sm">✨</span> 荣耀徽标
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-b-3 border-transparent border-b-yellow-300"></div>
                  </div>
                </a>
              {:else}
                <a href="/profile" class="relative group">
                  <div class="w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200">
                    <span class="text-white text-xs font-bold">
                      {session.user.username?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                </a>
              {/if}
              <div class="relative group">
                <a
                  href="/profile"
                  class="text-xs sm:text-sm font-medium text-gray-700 max-w-12 sm:max-w-20 truncate hidden sm:inline hover:text-green-600 transition-colors px-1 py-1"
                >
                  {session.user.username || 'User'}
                </a>
                <!-- 个人中心悬停提示 -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-[9999]">
                  进入个人中心
                  <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
                </div>
              </div>
            </div>

            <!-- 退出按钮 -->
            <button
              on:click={handleSignOut}
              class="px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 border border-red-200 hover:border-red-300 transition-all duration-200"
            >
              <span class="hidden sm:inline">退出</span>
              <span class="sm:hidden">🚪</span>
            </button>
          </div>
        {/if}

        {#if !session}
          <div class="flex items-center">
            <a
              href="/signin"
              class="px-6 py-2 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              登录
            </a>
          </div>
        {/if}
      </div>
    </div>
  </nav>
{/if}
