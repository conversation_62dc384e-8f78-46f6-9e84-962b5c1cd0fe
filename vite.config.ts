import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit()],
	build: {
		// 减少chunk大小警告限制到500kb
		chunkSizeWarningLimit: 500,
		rollupOptions: {
			output: {
				manualChunks: (id) => {
					// 处理UniverJS相关包
					// if (id.includes('@univerjs/core') || id.includes('@univerjs/design')) {
					// 	return 'univer-core'
					// }
					// if (id.includes('@univerjs/engine-render') || id.includes('@univerjs/engine-formula')) {
					// 	return 'univer-render'
					// }
					// if (id.includes('@univerjs/ui') || id.includes('@univerjs/docs')) {
					// 	return 'univer-ui'
					// }
					// if (id.includes('@univerjs/sheets') && !id.includes('@univerjs/sheets-')) {
					// 	return 'univer-sheets'
					// }
					// if (id.includes('@univerjs/sheets-formula')) {
					// 	return 'univer-formula'
					// }
					// if (id.includes('@univerjs/sheets-numfmt')) {
					// 	return 'univer-numfmt'
					// }
					// if (id.includes('@univerjs/data-validation') || id.includes('@univerjs/sheets-data-validation')) {
					// 	return 'univer-validation'
					// }
					// if (id.includes('@univerjs/sheets-filter') || id.includes('@univerjs/sheets-sort')) {
					// 	return 'univer-filter-sort'
					// }
					// if (id.includes('@univerjs/sheets-conditional-formatting')) {
					// 	return 'univer-conditional'
					// }
					// if (id.includes('@univerjs/sheets-table')) {
					// 	return 'univer-table'
					// }
					// if (id.includes('@univerjs-pro/sheets-chart') || id.includes('@univerjs-pro/sheets-pivot')) {
					// 	return 'univer-advanced'
					// }

					// 处理其他大的依赖包
					if (id.includes('node_modules')) {
						if (id.includes('@auth/')) {
							return 'vendor-auth'
						}
						if (id.includes('@prisma/') || id.includes('prisma')) {
							return 'vendor-db'
						}
						if (id.includes('bcryptjs') || id.includes('jsonwebtoken') ||
							id.includes('nodemailer') || id.includes('pg') || id.includes('rxjs')) {
							return 'vendor-utils'
						}
						// 其他大的第三方库
						if (id.includes('tailwindcss') || id.includes('autoprefixer') || id.includes('postcss')) {
							return 'vendor-css'
						}
					}
				}
			}
		}
	},
	optimizeDeps: {
		// 预构建依赖
		include: [
			'@univerjs/core',
			'@univerjs/engine-render',
			'@univerjs/engine-formula',
			'@univerjs/sheets',
			'@univerjs/sheets-ui',
			'@univerjs/facade'
		]
	}
});
