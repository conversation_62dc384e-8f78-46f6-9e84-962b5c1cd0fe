import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkUserVerification() {
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      },
      select: {
        id: true,
        email: true,
        username: true,
        emailVerified: true,
        emailVerificationToken: true,
        emailVerificationExpires: true
      }
    })

    if (user) {
      console.log('User found:')
      console.log('Email:', user.email)
      console.log('Username:', user.username)
      console.log('Email Verified:', user.emailVerified)
      console.log('Verification Token:', user.emailVerificationToken)
      console.log('Verification Expires:', user.emailVerificationExpires)
    } else {
      console.log('User not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserVerification()
