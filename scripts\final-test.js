// 最终测试脚本
import { chromium } from '@playwright/test'

async function finalTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🎯 开始最终测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值
    const initialNavText = await page.textContent('nav')
    const initialScore = initialNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 初始经验值:', initialScore)
    
    // 3. 进入初识表格
    await page.click('text=初识表格')
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入初识表格关卡')
    
    // 4. 查找数据输入任务的链接
    const taskLinks = await page.locator('a[href^="/task/"]').all()
    console.log(`📋 找到 ${taskLinks.length} 个任务链接`)
    
    // 5. 查找包含"数据输入"的卡片中的链接
    const dataInputCard = page.locator('h3:has-text("数据输入")').locator('..')
    const dataInputButton = dataInputCard.locator('a[href^="/task/"]')
    
    if (await dataInputButton.isVisible()) {
      const buttonText = await dataInputButton.textContent()
      console.log('🎯 数据输入按钮文本:', buttonText)
      
      // 6. 点击数据输入任务
      await dataInputButton.click()
      await page.waitForLoadState('networkidle')
      console.log('✅ 进入数据输入任务页面')
      
      // 7. 等待页面加载完成
      await page.waitForTimeout(2000)
      
      // 8. 查找完成按钮
      const completeButton = page.locator('button:has-text("完成任务"), button:has-text("提交答案"), button:has-text("完成"), button:has-text("提交")').first()
      
      if (await completeButton.isVisible()) {
        console.log('✅ 找到完成按钮，点击中...')
        
        // 监听网络请求
        page.on('response', response => {
          if (response.url().includes('/api/progress')) {
            console.log('📡 进度API响应:', response.status())
          }
        })
        
        await completeButton.click()
        console.log('✅ 点击完成按钮')
        
        // 等待API响应
        await page.waitForTimeout(5000)
        
        // 9. 检查是否有成功提示
        const pageContent = await page.textContent('body')
        if (pageContent.includes('恭喜') || pageContent.includes('完成') || pageContent.includes('成功')) {
          console.log('✅ 看到成功提示')
        } else {
          console.log('⚠️ 未看到明显的成功提示')
        }
        
        // 10. 返回dashboard检查经验值
        await page.goto('http://localhost:5173/dashboard')
        await page.waitForLoadState('networkidle')
        await page.waitForTimeout(2000)
        
        const updatedNavText = await page.textContent('nav')
        const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
        console.log('📊 更新后经验值:', updatedScore)
        
        if (parseInt(updatedScore) > parseInt(initialScore)) {
          console.log('🎉 经验值成功更新！')
        } else {
          console.log('❌ 经验值未更新')
        }
        
        // 11. 再次检查数据输入按钮状态
        await page.click('text=初识表格')
        await page.waitForLoadState('networkidle')
        
        const updatedDataInputCard = page.locator('h3:has-text("数据输入")').locator('..')
        const updatedDataInputButton = updatedDataInputCard.locator('a[href^="/task/"]')
        const updatedButtonText = await updatedDataInputButton.textContent()
        console.log('🎯 更新后按钮文本:', updatedButtonText)
        
        if (updatedButtonText && updatedButtonText.includes('再次挑战')) {
          console.log('🎉 按钮状态成功更新为"再次挑战"！')
        } else if (updatedButtonText && updatedButtonText.includes('开始挑战')) {
          console.log('⚠️ 按钮仍显示"开始挑战"')
        }
        
      } else {
        console.log('❌ 未找到完成按钮')
        const allButtons = await page.locator('button').allTextContents()
        console.log('页面所有按钮:', allButtons)
      }
    } else {
      console.log('❌ 未找到数据输入任务链接')
    }
    
    console.log('🎉 测试完成！浏览器将保持打开30秒供检查...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    await page.screenshot({ path: 'final-test-error.png' })
  } finally {
    await browser.close()
  }
}

finalTest()
