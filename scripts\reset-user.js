// 重置测试用户数据
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function resetUser() {
  try {
    console.log('🔄 重置测试用户数据...')
    
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!testUser) {
      console.log('❌ 测试用户不存在')
      return
    }
    
    // 删除所有进度记录
    await prisma.userProgress.deleteMany({
      where: { userId: testUser.id }
    })
    console.log('✅ 删除所有进度记录')
    
    // 重置用户积分
    await prisma.user.update({
      where: { id: testUser.id },
      data: { score: 0 }
    })
    console.log('✅ 重置用户积分为0')
    
    console.log('🎉 用户数据重置完成！')
    
  } catch (error) {
    console.error('❌ 重置失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

resetUser()
