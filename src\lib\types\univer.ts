// 单元格数据类型定义
export interface CellData {
  v: unknown; // 单元格值
  s?: Record<string, unknown>; // 样式数据
  f?: string; // 公式
  t?: number; // 数据类型
  p?: Record<string, unknown>; // 其他属性
  rowSpan?: number; // 行跨度（合并单元格）
  colSpan?: number; // 列跨度（合并单元格）
}

// 筛选器相关类型定义
export interface UniverFilter {
  getRange(): { getA1Notation(): string };
  getFilteredOutRows(): unknown[];
  getColumnFilterCriteria(colIndex: number): unknown;
}

// 定义筛选器的类型
export interface FilterAPI {
  getRange?(): { getA1Notation?(): string };
  getFilteredOutRows?(): unknown[];
  getColumnFilterCriteria?(colIndex: number): unknown;
}

// 工作表数据类型
export interface WorksheetData {
  id: string;
  name: string;
  cellData: Record<number, Record<number, CellData>>;
  rowCount?: number;
  columnCount?: number;
  [key: string]: unknown;
}

// 工作簿数据类型
export interface WorkbookData {
  id: string;
  locale: string;
  name: string;
  sheetOrder: string[];
  sheets: Record<string, WorksheetData>;
  [key: string]: unknown;
}

export interface UniverInstance {
  dispose?: () => void;
  getWorkbook?: () => any;
  getActiveSheet?: () => any;
  createUnit?: (type: any, data: WorkbookData) => void;
  registerPlugin?: (plugin: any, config?: any) => void;
  registerPlugins?: (plugins: any[]) => void;
  [key: string]: any;
}

export interface UniverWorkbook {
  getActiveSheet(): UniverWorksheet | null;
  getSheetBySheetId?(sheetId: string): UniverWorksheet | null;
  getSheetCount?(): number;
}

export interface UniverAPI {
  getActiveWorkbook(): UniverWorkbook | null;
  getSheetData?: () => any;
  setSheetData?: (data: any) => void;
  getCellValue?: (row: number, col: number) => any;
  setCellValue?: (row: number, col: number, value: any) => void;
  dispose?: () => void;
  [key: string]: any;
}



export interface UniverWorksheet {
  getSheetId(): string;
  getName(): string;
  getCellData(): Record<string, Record<string, CellData>>;
  getRange(range: string): UniverRange;
  getCell(row: number, col: number): CellData;
  getFilter(): unknown | null;
  // 兼容通过行列号获取范围
  getRangeByIndexes?(row: number, col: number): UniverRange;
}

export interface UniverRange {
  getValue(): unknown;
  getValues(): unknown[][];
  getFormula(): string;
  getFormulas(): string[][];
  getBackgroundColor(): string;
  getBackgroundColors(): string[][];
  getCellData(): CellData;
  getNumberFormat(): string;
  getDisplayValue(): string;
  getConditionalFormattingRules(): unknown[];
  isMerged(): boolean;
  getWrap(): boolean;
}

export type UniverReadyCallback = (instance: UniverInstance, api: UniverAPI) => void

export interface ValidationRule {
  type: string
  cell?: string
  range?: string
  dataRange?: string
  expectedValue?: unknown
  condition?: string
  value?: unknown
  expectedBackgroundColor?: string
  expectedFontColor?: string
  expectedBorder?: string
  expectedAlignment?: string
  expectedFormat?: string
  expectedFormula?: string
  expectedDataValidation?: unknown
  expectedConditionalFormat?: unknown
  expectedPivotTable?: unknown
  expectedChart?: unknown
  expectedVisibleRows?: number
  expectedFilteredData?: unknown[]
  expectedOrder?: string[]
  expectedFormulas?: Array<{
    cell: string
    formula: string
  }>
  // 排序相关
  column?: string
  direction?: string
  sortDirection?: string
  sorts?: Array<{
    column: string
    direction: string
  }>
  // 数据验证相关
  validationType?: string
  source?: string
  allowDropdown?: boolean
  // 条件格式相关
  expectedFormattedCells?: string[]
  conditions?: Array<{
    type: string
    value?: number
    minValue?: number
    maxValue?: number
    color: string
  }>
  expectedResults?: Record<string, string[]>
  // 合并单元格相关
  mergedRanges?: Array<{
    range: string
    description?: string
  }>
  expectedStyle?: {
    bold?: boolean
    italic?: boolean
    fontFamily?: string
    fontSize?: number
    [key: string]: unknown
  }
  cells?: Array<{
    range?: string
    cell?: string
    expectedBorder?: string
    expectedBorderColor?: string
    expectedAlignment?: string
    wrapType?: string
    expectedText?: string
    description?: string
    [key: string]: unknown
  }>
  [key: string]: unknown
}

export interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}
