import { dev } from '$app/environment'
import type { RequestHandler } from './$types'

export const GET: RequestHandler = async ({ request, url }) => {
  // 优先从代理头部获取原始域名和协议
  const host = 
    request.headers.get('x-forwarded-host') || // Vercel/AWS/Nginx 标准
    request.headers.get('host') || // 直接访问时的备用
    url.host || // SvelteKit URL对象
    'localhost:5173' // 回退值

  const protocol = 'https' // 生产环境使用https
    // request.headers.get('x-forwarded-proto') || // 标准代理协议头
    // url.protocol.replace(':', '') // 根据URL判断

  const baseUrl = `${protocol}://${host}`

  // 根据环境变量决定是否允许爬虫
  const isProduction = !dev

  const robotsContent = `User-agent: *
${isProduction ? 'Allow: /' : 'Disallow: /'}

# Baidu spider optimization
User-agent: Bai<PERSON>pider
${isProduction ? 'Allow: /' : 'Disallow: /'}
Crawl-delay: 1

# Bing spider optimization
User-agent: bingbot
${isProduction ? 'Allow: /' : 'Disallow: /'}
Crawl-delay: 1

# Google spider optimization
User-agent: Googlebot
${isProduction ? 'Allow: /' : 'Disallow: /'}

# Disallowed paths for all crawlers
Disallow: /api/
Disallow: /auth/
Disallow: /_app/
Disallow: /admin/
Disallow: /profile/
Disallow: /dashboard/
Disallow: /task/
Disallow: /level/
Disallow: /invite-codes/

# Allowed public paths
Allow: /legal/
Allow: /

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml`

  return new Response(robotsContent, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
    },
  })
}
