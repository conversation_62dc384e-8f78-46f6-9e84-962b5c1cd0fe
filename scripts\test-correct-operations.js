import { chromium } from 'playwright';

async function testCorrectOperations() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 监听控制台输出
  page.on('console', msg => {
    if (msg.text().includes('验证') || msg.text().includes('字体') || msg.text().includes('样式')) {
      console.log(`🖥️ [${msg.type()}] ${msg.text()}`);
    }
  });
  
  try {
    console.log('🔍 开始测试正确操作...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 测试字体更改任务
    console.log('\n📝 测试字体更改任务（正确操作）...')
    await page.goto('http://localhost:5173/task/cmdsdv4z1000hu4804evogf7v')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 执行正确操作：选中B1单元格，设置字体为宋体')
    
    // 等待Univer加载完成
    await page.waitForSelector('.univer-container', { timeout: 10000 })
    
    // 尝试点击B1单元格
    console.log('  📍 尝试点击B1单元格...')
    
    // 等待一下确保表格完全加载
    await page.waitForTimeout(3000)
    
    // 尝试多种方式选中B1单元格
    try {
      // 方法1：通过坐标点击（假设B1在特定位置）
      const univerContainer = await page.locator('.univer-container').first()
      const box = await univerContainer.boundingBox()
      if (box) {
        // 点击B1单元格的大概位置（第二列第一行）
        const cellX = box.x + 120 // 假设每列约60px宽
        const cellY = box.y + 80  // 假设标题行约40px高
        await page.mouse.click(cellX, cellY)
        console.log('  ✅ 已点击B1单元格位置')
      }
    } catch (e) {
      console.log('  ⚠️ 点击B1单元格失败，继续尝试其他方法')
    }
    
    await page.waitForTimeout(1000)
    
    // 尝试设置字体
    console.log('  🎨 尝试设置字体为宋体...')
    
    // 方法1：尝试通过工具栏设置字体
    try {
      // 查找字体下拉框
      const fontDropdown = page.locator('select, .font-family, [class*="font"]').first()
      if (await fontDropdown.isVisible()) {
        await fontDropdown.click()
        await page.waitForTimeout(500)
        
        // 尝试选择宋体
        const songtiOption = page.locator('option, .option, [class*="option"]').filter({ hasText: /宋体|SimSun/ })
        if (await songtiOption.count() > 0) {
          await songtiOption.first().click()
          console.log('  ✅ 已选择宋体字体')
        }
      }
    } catch (e) {
      console.log('  ⚠️ 通过下拉框设置字体失败')
    }
    
    await page.waitForTimeout(2000)
    
    // 方法2：尝试通过键盘快捷键或其他方式
    console.log('  ⌨️ 尝试其他设置方式...')
    
    // 等待一下让操作生效
    await page.waitForTimeout(3000)
    
    console.log('  🚀 提交任务验证...')
    
    // 提交任务
    let submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
    await submitButton.click()
    
    // 等待验证完成
    await page.waitForTimeout(5000)
    
    // 检查验证结果
    const validationMessages = await page.locator('div, span, p').filter({ hasText: /验证|失败|成功|错误|通过|完成/ }).allTextContents()
    console.log('🔍 验证结果:', validationMessages)
    
    const hasSuccess = validationMessages.some(msg => 
      msg.includes('验证通过') || 
      msg.includes('任务完成') || 
      msg.includes('闯关完成') ||
      msg.includes('恭喜')
    )
    
    const hasFailure = validationMessages.some(msg => 
      msg.includes('验证失败') || 
      msg.includes('闯关失败') || 
      msg.includes('不正确') ||
      msg.includes('错误')
    )
    
    console.log(`\n📊 字体更改任务结果：`)
    console.log(`  成功: ${hasSuccess}`)
    console.log(`  失败: ${hasFailure}`)
    
    if (hasSuccess) {
      console.log('  ✅ 正确操作成功通过验证')
    } else if (hasFailure) {
      console.log('  ⚠️ 正确操作未能通过验证，可能需要调整操作方式')
    } else {
      console.log('  ❓ 验证结果不明确')
    }
    
    console.log('\n📊 测试完成')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  } finally {
    console.log('\n🔍 浏览器保持打开状态，请手动检查页面')
    // 不关闭浏览器，让用户可以手动检查
    // await browser.close()
  }
}

testCorrectOperations()
