// 控制台日志测试
import { chromium } from '@playwright/test'

async function consoleTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  // 监听控制台消息
  page.on('console', msg => {
    console.log(`🖥️ 浏览器控制台 [${msg.type()}]:`, msg.text())
  })
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.error('❌ 页面错误:', error.message)
  })
  
  // 监听网络请求
  page.on('request', request => {
    if (request.url().includes('/api/progress')) {
      console.log(`📡 进度API请求: ${request.method()} ${request.url()}`)
      if (request.postData()) {
        console.log(`📝 请求数据: ${request.postData()}`)
      }
    }
  })
  
  page.on('response', response => {
    if (response.url().includes('/api/progress')) {
      console.log(`📨 进度API响应: ${response.status()} ${response.url()}`)
    }
  })
  
  try {
    console.log('🎯 开始控制台测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值
    const initialNavText = await page.textContent('nav')
    const initialScore = initialNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 初始经验值:', initialScore)
    
    // 3. 直接访问数据输入任务页面
    const taskId = 'cmdsdv4yp0009u480e9dd3pw8'
    await page.goto(`http://localhost:5173/task/${taskId}`)
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入数据输入任务页面')
    
    // 4. 等待页面加载
    await page.waitForTimeout(3000)
    
    // 5. 点击提交按钮
    const submitButton = page.locator('button:has-text("提交任务")').first()
    if (await submitButton.isVisible()) {
      console.log('✅ 找到提交按钮，点击中...')
      await submitButton.click()
      console.log('✅ 点击提交按钮')
      
      // 等待处理完成
      await page.waitForTimeout(8000)
      
    } else {
      console.log('❌ 未找到提交按钮')
    }
    
    // 6. 返回dashboard检查经验值
    await page.goto('http://localhost:5173/dashboard')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    const updatedNavText = await page.textContent('nav')
    const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 更新后经验值:', updatedScore)
    
    if (parseInt(updatedScore) > parseInt(initialScore)) {
      console.log('🎉 经验值成功更新！')
    } else {
      console.log('❌ 经验值未更新')
    }
    
    console.log('🎉 控制台测试完成！浏览器将保持打开30秒...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 控制台测试失败:', error)
    await page.screenshot({ path: 'console-test-error.png' })
  } finally {
    await browser.close()
  }
}

consoleTest()
