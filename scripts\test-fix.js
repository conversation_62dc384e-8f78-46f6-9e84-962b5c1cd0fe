// 测试修复后的功能
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testFix() {
  try {
    console.log('🔍 开始测试修复后的功能...')
    
    // 1. 检查测试用户是否存在
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { id: true, email: true, username: true, score: true, userType: true }
    })
    
    if (!testUser) {
      console.log('❌ 测试用户不存在')
      return
    }
    
    console.log('✅ 测试用户存在:', testUser)
    
    // 2. 检查是否有关卡数据
    const levels = await prisma.level.findMany({
      where: { isMainTask: false },
      take: 3,
      include: {
        progress: {
          where: { userId: testUser.id }
        }
      }
    })
    
    console.log(`✅ 找到 ${levels.length} 个子关卡`)
    
    // 3. 模拟完成一个任务
    if (levels.length > 0) {
      const firstLevel = levels[0]
      console.log(`🎯 模拟完成关卡: ${firstLevel.name}`)
      
      // 检查是否已有进度记录
      let progress = await prisma.userProgress.findUnique({
        where: {
          userId_levelId: {
            userId: testUser.id,
            levelId: firstLevel.id
          }
        }
      })
      
      if (progress) {
        console.log('📊 已存在进度记录:', progress)
        
        // 更新进度（模拟再次挑战）
        progress = await prisma.userProgress.update({
          where: { id: progress.id },
          data: {
            completed: true,
            score: firstLevel.points,
            attempts: progress.attempts + 1,
            completedAt: new Date()
          }
        })
        console.log('🔄 更新进度记录成功')
      } else {
        // 创建新进度记录
        progress = await prisma.userProgress.create({
          data: {
            userId: testUser.id,
            levelId: firstLevel.id,
            completed: true,
            score: firstLevel.points,
            attempts: 1,
            completedAt: new Date()
          }
        })
        console.log('🆕 创建新进度记录成功')
        
        // 更新用户总积分
        const updatedUser = await prisma.user.update({
          where: { id: testUser.id },
          data: {
            score: {
              increment: firstLevel.points
            }
          }
        })
        console.log(`💎 用户积分更新: ${testUser.score} -> ${updatedUser.score}`)
      }
    }
    
    // 4. 检查最终状态
    const finalUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { score: true }
    })
    
    const userProgress = await prisma.userProgress.findMany({
      where: { userId: testUser.id },
      include: { level: true }
    })
    
    console.log('📈 最终用户积分:', finalUser?.score)
    console.log('📋 用户进度记录数量:', userProgress.length)
    
    if (userProgress.length > 0) {
      console.log('📝 进度记录详情:')
      userProgress.forEach(p => {
        console.log(`  - ${p.level.name}: ${p.completed ? '已完成' : '未完成'} (${p.score}分, ${p.attempts}次尝试)`)
      })
    }
    
    console.log('✅ 测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testFix()
