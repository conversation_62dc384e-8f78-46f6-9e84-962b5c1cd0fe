import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  // 连接池配置 - 优化多进程环境下的数据库连接
  // 参考: https://www.prisma.io/docs/concepts/components/prisma-client/working-with-prismaclient/connection-pool
  // 在集群模式下，每个进程都有自己的连接池
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 优雅关闭数据库连接
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})