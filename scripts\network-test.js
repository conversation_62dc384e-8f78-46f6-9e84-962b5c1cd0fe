// 网络请求监听测试
import { chromium } from '@playwright/test'

async function networkTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  // 监听所有网络请求
  const requests = []
  page.on('request', request => {
    if (request.url().includes('/api/')) {
      requests.push({
        url: request.url(),
        method: request.method(),
        postData: request.postData(),
        timestamp: new Date().toISOString()
      })
      console.log(`📡 请求: ${request.method()} ${request.url()}`)
      if (request.postData()) {
        console.log(`📝 请求数据: ${request.postData()}`)
      }
    }
  })
  
  page.on('response', response => {
    if (response.url().includes('/api/')) {
      console.log(`📨 响应: ${response.status()} ${response.url()}`)
    }
  })
  
  try {
    console.log('🌐 开始网络监听测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值
    const initialNavText = await page.textContent('nav')
    const initialScore = initialNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 初始经验值:', initialScore)
    
    // 3. 直接访问数据输入任务页面
    const taskId = 'cmdsdv4yp0009u480e9dd3pw8'
    await page.goto(`http://localhost:5173/task/${taskId}`)
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入数据输入任务页面')
    
    // 4. 等待页面加载
    await page.waitForTimeout(3000)
    
    // 5. 清空之前的请求记录
    requests.length = 0
    console.log('🧹 清空请求记录，开始监听提交请求...')
    
    // 6. 点击提交按钮
    const submitButton = page.locator('button:has-text("提交任务")').first()
    if (await submitButton.isVisible()) {
      console.log('✅ 找到提交按钮，点击中...')
      await submitButton.click()
      console.log('✅ 点击提交按钮')
      
      // 等待网络请求完成
      await page.waitForTimeout(8000)
      
      console.log('📊 捕获到的API请求:')
      requests.forEach((req, index) => {
        console.log(`${index + 1}. ${req.method} ${req.url}`)
        if (req.postData) {
          console.log(`   数据: ${req.postData}`)
        }
        console.log(`   时间: ${req.timestamp}`)
      })
      
      if (requests.length === 0) {
        console.log('❌ 没有捕获到任何API请求')
      }
      
      // 7. 检查页面是否有成功提示
      const pageContent = await page.textContent('body')
      if (pageContent.includes('恭喜') || pageContent.includes('完成') || pageContent.includes('成功')) {
        console.log('✅ 看到成功提示')
      } else {
        console.log('⚠️ 未看到明显的成功提示')
      }
      
      // 8. 返回dashboard检查经验值
      await page.goto('http://localhost:5173/dashboard')
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(2000)
      
      const updatedNavText = await page.textContent('nav')
      const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
      console.log('📊 更新后经验值:', updatedScore)
      
      if (parseInt(updatedScore) > parseInt(initialScore)) {
        console.log('🎉 经验值成功更新！')
      } else {
        console.log('❌ 经验值未更新')
      }
      
    } else {
      console.log('❌ 未找到提交按钮')
    }
    
    console.log('🎉 网络测试完成！浏览器将保持打开30秒...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 网络测试失败:', error)
    await page.screenshot({ path: 'network-test-error.png' })
  } finally {
    await browser.close()
  }
}

networkTest()
