import { chromium } from 'playwright';

const TEST_TASKS = [
  {
    name: '字体更改',
    taskId: 'cmdsdv4z1000hu4804evogf7v',
    description: '测试字体更改验证是否正确工作'
  },
  {
    name: '字体颜色设置',
    taskId: 'cmdsdv4z7000lu480dduqvycf',
    description: '测试字体颜色设置验证是否正确工作'
  },
  {
    name: '单元格对齐方式',
    taskId: 'cmdsdv4zg000tu480ftvetu5f',
    description: '测试对齐方式验证是否正确工作'
  },
  {
    name: '数据透视表',
    taskId: 'cmdsdv51t002tu480ai1y58eo',
    description: '测试数据透视表验证是否正确工作'
  }
];

async function testValidationFixes() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔍 开始验证修复测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 测试每个任务
    for (const task of TEST_TASKS) {
      console.log(`\n📝 测试任务: ${task.name}`)
      console.log(`   描述: ${task.description}`)
      
      await page.goto(`http://localhost:5173/task/${task.taskId}`)
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(5000)
      
      console.log('  🔸 第一次测试：不做任何操作，直接提交（应该失败）')
      
      // 直接提交（不做任何操作）
      let submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
      await submitButton.click()
      await page.waitForTimeout(3000)
      
      // 检查是否有验证结果
      let pageContent = await page.content()
      const hasError = pageContent.includes('验证失败') || 
                      pageContent.includes('不正确') || 
                      pageContent.includes('错误') ||
                      pageContent.includes('未设置') ||
                      pageContent.includes('缺少')
      const hasSuccess = pageContent.includes('验证通过') || 
                        pageContent.includes('任务完成') || 
                        pageContent.includes('恭喜')
      
      console.log(`    结果：失败=${hasError}, 成功=${hasSuccess}`)
      
      if (hasError && !hasSuccess) {
        console.log(`    ✅ ${task.name} - 空操作正确地被拒绝`)
      } else {
        console.log(`    ❌ ${task.name} - 空操作错误地被接受`)
      }
      
      // 等待一下再继续下一个任务
      await page.waitForTimeout(2000)
    }
    
    console.log('\n📊 测试完成')
    console.log('='.repeat(50))
    console.log('请查看上面的结果，验证修复是否有效')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  } finally {
    await browser.close()
  }
}

testValidationFixes()
