import type { Handle } from '@sveltejs/kit'
import { handle as authHandle } from '$lib/auth'
import { sequence } from '@sveltejs/kit/hooks'

// 自定义处理器
const customHandle: Handle = async ({ event, resolve }) => {
  // 添加 auth 方法到 locals
  event.locals.auth = async () => {
    // 这里可以添加自定义的认证逻辑
    return null
  }

  return resolve(event)
}

// 组合 Auth.js 处理器和自定义处理器
export const handle = sequence(authHandle, customHandle)