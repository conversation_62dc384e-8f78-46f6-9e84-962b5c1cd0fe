// 测试登录修复的脚本
const fetch = require('node-fetch');

const BASE_URL = 'http://115.190.155.92:4000';

async function testLoginFix() {
  console.log('测试登录修复...\n');

  try {
    // 1. 获取登录页面（获取CSRF token等）
    console.log('1. 获取登录页面...');
    const loginPageResponse = await fetch(`${BASE_URL}/login`);
    console.log(`登录页面状态码: ${loginPageResponse.status}`);
    
    // 2. 模拟登录请求
    console.log('\n2. 测试登录请求...');
    const formData = new URLSearchParams();
    formData.append('email', '<EMAIL>');
    formData.append('password', '123456');
    
    const loginResponse = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
      redirect: 'manual' // 不自动跟随重定向
    });
    
    console.log(`登录请求状态码: ${loginResponse.status}`);
    
    if (loginResponse.status === 302) {
      const location = loginResponse.headers.get('location');
      console.log(`重定向到: ${location}`);
      
      if (location === '/dashboard') {
        console.log('✅ 登录重定向正常！');
      } else {
        console.log('❌ 重定向地址不正确');
      }
    } else if (loginResponse.status === 400) {
      const responseText = await loginResponse.text();
      console.log('❌ 登录失败，响应内容:');
      console.log(responseText.substring(0, 500) + '...');
    } else {
      console.log(`❌ 意外的状态码: ${loginResponse.status}`);
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testLoginFix();
}

module.exports = { testLoginFix };
