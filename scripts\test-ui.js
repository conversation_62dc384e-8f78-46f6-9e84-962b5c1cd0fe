// 简单的UI测试脚本
import { chromium } from 'playwright'

async function testUI() {
  const browser = await chromium.launch({ headless: false })
  const page = await browser.newPage()
  
  try {
    console.log('🌐 开始UI测试...')
    
    // 1. 访问登录页面
    await page.goto('http://localhost:5174/login')
    console.log('✅ 访问登录页面成功')
    
    // 2. 登录
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', '123456')
    await page.click('button[type="submit"]')
    
    // 等待重定向到dashboard
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功，重定向到dashboard')
    
    // 3. 检查导航栏经验值显示
    const navScore = await page.textContent('.text-blue-700')
    console.log('📊 导航栏显示经验值:', navScore)
    
    // 4. 检查dashboard总经验值显示
    const dashboardScore = await page.locator('text=总经验值').locator('..').locator('dd').first().textContent()
    console.log('📈 Dashboard显示总经验值:', dashboardScore)
    
    // 5. 点击第一个主任务
    await page.click('a[href^="/level/"]')
    await page.waitForLoadState('networkidle')
    console.log('✅ 进入关卡页面')
    
    // 6. 检查按钮文本（应该显示"再次挑战"因为我们已经完成了一个任务）
    const buttonText = await page.locator('text=🔄 再次挑战, 🚀 开始挑战').first().textContent()
    console.log('🎯 关卡按钮文本:', buttonText)
    
    // 7. 点击任务进入任务页面
    if (buttonText && buttonText.includes('再次挑战')) {
      console.log('✅ 按钮正确显示"再次挑战"')
    } else if (buttonText && buttonText.includes('开始挑战')) {
      console.log('✅ 按钮正确显示"开始挑战"')
    }
    
    console.log('✅ UI测试完成！')
    
  } catch (error) {
    console.error('❌ UI测试失败:', error)
  } finally {
    await browser.close()
  }
}

testUI()
