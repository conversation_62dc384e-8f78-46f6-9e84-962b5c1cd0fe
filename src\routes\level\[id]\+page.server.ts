import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'
import { prisma } from '$lib/db'
import { getSession } from '$lib/session'
import { hasAdvancedAccess, getLevelAccessInfo } from '$lib/invite-codes'

export const load: PageServerLoad = async ({ params, cookies }) => {
  const session = await getSession(cookies)

  if (!session?.user) {
    throw redirect(302, '/signin')
  }

  const levelId = params.id

  try {
    // 获取所有主任务
    const mainTasks = await prisma.level.findMany({
      where: {
        parentId: null
      },
      include: {
        children: {
          include: {
            tasks: {
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: {
                userId: session.user.id
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    })

    // 查找当前主任务
    const currentMainTask = mainTasks.find(task => task.id === levelId)

    if (!currentMainTask) {
      throw redirect(302, '/dashboard')
    }

    // 检查是否是受限关卡
    const isAdvancedLevel = currentMainTask.name === '进阶操作' || currentMainTask.name === '实用技巧'
    let isLocked = false

    if (isAdvancedLevel) {
      // 检查用户是否有邀请码或足够的经验值
      const user = await prisma.user.findUnique({
        where: { id: session.user.id }
      })

      if (user) {
        // 使用统一的权限检查函数
        const accessInfo = getLevelAccessInfo(currentMainTask.name, user.userType, user.score)
        isLocked = accessInfo.isLocked

        // 设置主任务的锁定状态
        const mainTaskWithLock = {
          ...currentMainTask,
          isLocked: accessInfo.isLocked,
          hasAccess: accessInfo.hasAccess,
          buttonText: accessInfo.buttonText,
          requiredScore: accessInfo.requiredScore
        }

        return {
          session,
          mainTask: mainTaskWithLock
        }
      }
    }

    // 如果没有用户信息，使用默认设置
    const mainTaskWithLock = {
      ...currentMainTask,
      isLocked: true,
      hasAccess: false,
      buttonText: '需要登录',
      requiredScore: 0
    }

    return {
      session,
      mainTask: mainTaskWithLock
    }
  } catch (error) {
    console.error('获取关卡数据失败:', error)
    throw redirect(302, '/dashboard')
  }
}
