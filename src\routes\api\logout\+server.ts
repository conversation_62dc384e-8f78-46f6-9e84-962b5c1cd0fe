import type { RequestHandler } from './$types'
import { redirect } from '@sveltejs/kit'
import { destroySession } from '$lib/session'

export const POST: RequestHandler = async ({ cookies }) => {
  // 销毁自定义session
  destroySession(cookies)

  // 清除Auth.js相关的cookies
  const authCookies = [
    'authjs.session-token',
    'authjs.csrf-token',
    'authjs.callback-url',
    '__Secure-authjs.session-token',
    '__Host-authjs.csrf-token'
  ]

  authCookies.forEach(cookieName => {
    cookies.delete(cookieName, { path: '/' })
    cookies.delete(cookieName, { path: '/', domain: undefined })
  })

  // 重定向到首页
  throw redirect(302, '/')
}

export const GET: RequestHandler = async ({ cookies }) => {
  // 销毁自定义session
  destroySession(cookies)

  // 清除Auth.js相关的cookies
  const authCookies = [
    'authjs.session-token',
    'authjs.csrf-token',
    'authjs.callback-url',
    '__Secure-authjs.session-token',
    '__Host-authjs.csrf-token'
  ]

  authCookies.forEach(cookieName => {
    cookies.delete(cookieName, { path: '/' })
    cookies.delete(cookieName, { path: '/', domain: undefined })
  })

  // 重定向到首页
  throw redirect(302, '/')
}
