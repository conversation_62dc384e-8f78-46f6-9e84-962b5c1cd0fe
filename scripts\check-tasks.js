// 检查数据库中的任务名称
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkTasks() {
  try {
    console.log('🔍 检查数据库中的主任务...')
    
    const mainTasks = await prisma.level.findMany({
      where: { parentId: null },
      orderBy: { order: 'asc' },
      include: {
        children: {
          orderBy: { order: 'asc' },
          take: 3
        }
      }
    })
    
    console.log('📋 主任务列表:')
    mainTasks.forEach(task => {
      console.log(`- ID: ${task.id}`)
      console.log(`  名称: ${task.name}`)
      console.log(`  描述: ${task.description}`)
      console.log(`  子任务数量: ${task.children.length}`)
      if (task.children.length > 0) {
        console.log(`  子任务示例: ${task.children[0].name}`)
      }
      console.log('')
    })
    
  } catch (error) {
    console.error('❌ 检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkTasks()
