import { chromium } from 'playwright';

// 测试关键任务
const keyTasks = [
  { id: 'cmdsdv4z7000lu480dduqvycf', name: '字体颜色设置' },
  { id: 'cmdsdv4z1000hu4804evogf7v', name: '字体更改' },
  { id: 'cmdsdv4zc000pu480ozbjqgbm', name: '数字格式' },
  { id: 'cmdsdv51j002lu48094ov6c6w', name: '简单条件格式' }
];

async function testKeyTasks() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔍 开始关键任务测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    const results = []
    
    // 测试每个关键任务
    for (const task of keyTasks) {
      console.log(`\n📝 测试任务: ${task.name}`)
      
      try {
        // 访问任务页面
        await page.goto(`http://localhost:5174/task/${task.id}`)
        await page.waitForLoadState('networkidle')
        
        // 等待Univer加载
        await page.waitForTimeout(5000)

        // 调试：查看页面上的所有按钮
        const allButtons = await page.locator('button').all()
        console.log(`  🔍 页面上共有 ${allButtons.length} 个按钮`)
        for (let i = 0; i < Math.min(allButtons.length, 5); i++) {
          const buttonText = await allButtons[i].textContent()
          console.log(`    按钮 ${i + 1}: "${buttonText}"`)
        }

        // 第一轮测试：不做任何操作，直接提交
        console.log('  🔸 不做任何操作，直接提交...')
        
        // 尝试多种按钮选择器
        let submitButton = page.locator('button:has-text("🚀 提交任务")').first()
        if (!(await submitButton.isVisible())) {
          submitButton = page.locator('button:has-text("提交任务")').first()
        }
        if (!(await submitButton.isVisible())) {
          submitButton = page.locator('button:has-text("提交")').first()
        }
        if (!(await submitButton.isVisible())) {
          submitButton = page.locator('button[type="submit"]').first()
        }
        if (!(await submitButton.isVisible())) {
          submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
        }

        if (await submitButton.isVisible()) {
          await submitButton.click()
          
          // 等待验证完成
          await page.waitForTimeout(8000)
          
          // 检查页面内容来判断验证结果
          const pageContent = await page.content()
          
          // 查找成功或失败的指示
          const hasSuccess = pageContent.includes('验证通过') || 
                           pageContent.includes('任务完成') || 
                           pageContent.includes('恭喜') ||
                           pageContent.includes('成功')
          
          const hasError = pageContent.includes('验证失败') || 
                          pageContent.includes('不正确') || 
                          pageContent.includes('错误') ||
                          pageContent.includes('请')
          
          if (hasSuccess && !hasError) {
            console.log(`  ❌ 任务 ${task.name} 错误地通过了验证（应该失败）`)
            results.push({ task: task.name, status: 'FAIL', reason: '不做操作却通过验证' })
          } else if (hasError) {
            console.log(`  ✅ 任务 ${task.name} 正确地验证失败`)
            results.push({ task: task.name, status: 'PASS', reason: '正确拒绝空操作' })
          } else {
            console.log(`  ⚠️ 任务 ${task.name} 验证状态不明确`)
            console.log(`  页面内容片段: ${pageContent.substring(0, 500)}...`)
            results.push({ task: task.name, status: 'UNKNOWN', reason: '验证状态不明确' })
          }
        } else {
          console.log(`  ⚠️ 任务 ${task.name} 找不到提交按钮`)
          results.push({ task: task.name, status: 'ERROR', reason: '找不到提交按钮' })
        }
        
      } catch (error) {
        console.log(`  ❌ 任务 ${task.name} 测试出错: ${error.message}`)
        results.push({ task: task.name, status: 'ERROR', reason: error.message })
      }
    }
    
    // 输出测试结果
    console.log('\n📊 关键任务测试结果:')
    console.log('='.repeat(50))
    
    results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : 
                   result.status === 'FAIL' ? '❌' : 
                   result.status === 'ERROR' ? '⚠️' : '❓'
      console.log(`${icon} ${result.task}: ${result.reason}`)
    })
    
    const passCount = results.filter(r => r.status === 'PASS').length
    const failCount = results.filter(r => r.status === 'FAIL').length
    
    console.log(`\n总结: ${passCount}/${keyTasks.length} 个任务验证正确`)
    
    if (failCount === 0) {
      console.log('🎉 所有关键任务验证都正确！')
    } else {
      console.log('⚠️ 还有任务需要修复验证逻辑')
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    // 保持浏览器打开30秒供检查
    console.log('\n浏览器将保持打开30秒供检查...')
    await page.waitForTimeout(30000)
    await browser.close()
  }
}

testKeyTasks()
