import { chromium } from '@playwright/test'

async function testLogin() {
  const browser = await chromium.launch({ headless: false })
  const page = await browser.newPage()
  
  try {
    console.log('Navigating to login page...')
    await page.goto('http://localhost:5173/login')
    
    console.log('Taking screenshot of login page...')
    await page.screenshot({ path: 'login-page.png' })
    
    console.log('Filling login form...')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    
    console.log('Submitting form...')

    // 监听导航事件
    page.on('response', response => {
      console.log('Response:', response.status(), response.url())
    })

    await page.click('button[type="submit"]')

    // 等待重定向或错误消息
    try {
      await page.waitForURL('**/dashboard', { timeout: 10000 })
      console.log('Successfully redirected to dashboard!')
    } catch (error) {
      console.log('No redirect to dashboard, checking for errors...')

      // 等待一下看看页面是否有变化
      await page.waitForTimeout(2000)
    }

    console.log('Current URL:', page.url())
    await page.screenshot({ path: 'after-login.png' })

    // 检查是否有错误消息
    const errorElement = await page.$('.text-red-700')
    if (errorElement) {
      const errorText = await errorElement.textContent()
      console.log('Error message:', errorText)
    } else {
      console.log('No error message found')
    }

    // 检查页面内容
    const pageTitle = await page.title()
    console.log('Page title:', pageTitle)
    
  } catch (error) {
    console.error('Test failed:', error)
  } finally {
    await browser.close()
  }
}

testLogin()
