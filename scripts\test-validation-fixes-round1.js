/**
 * 第一轮测试：验证错误操作无法完成任务
 * 测试所有修复的验证逻辑，确保错误操作正确失败
 */

import { chromium } from 'playwright';

const TEST_TASKS = [
  {
    name: '字体更改',
    taskId: 'cmdsdv4z1000hu4804evogf7v',
    description: '测试空操作是否能完成字体更改任务',
    errorOperation: '不进行任何字体更改操作'
  },
  {
    name: '字体颜色设置',
    taskId: 'cmdsdv4z7000lu480dduqvycf',
    description: '测试空操作是否能完成字体颜色设置任务',
    errorOperation: '不进行任何字体颜色设置操作'
  },
  {
    name: '单元格对齐方式',
    taskId: 'cmdsdv4zg000tu480ftvetu5f',
    description: '测试空操作是否能完成对齐方式任务',
    errorOperation: '不进行任何对齐设置操作'
  },
  {
    name: '边框设置',
    taskId: 'cmdsdv4zl000xu480ucn3or6s',
    description: '测试空操作是否能完成边框设置任务',
    errorOperation: '不进行任何边框设置操作'
  },
  {
    name: '简单排序',
    taskId: 'cmdsdv50l001ru480cd7pwbr2',
    description: '测试空操作是否能完成排序任务',
    errorOperation: '不进行任何排序操作'
  },
  {
    name: '复杂排序',
    taskId: 'cmdsdv50q001vu480ot2phb21',
    description: '测试空操作是否能完成复杂排序任务',
    errorOperation: '不进行任何排序操作'
  },
  {
    name: '简单数据验证',
    taskId: 'cmdsdv50t001zu48011lbkwvt',
    description: '测试空操作是否能完成数据验证任务',
    errorOperation: '不设置任何数据验证规则'
  },
  {
    name: '复杂数据验证',
    taskId: 'cmdsdv50y0023u4809y8uohec',
    description: '测试空操作是否能完成复杂数据验证任务',
    errorOperation: '不设置任何数据验证规则'
  },
  {
    name: '简单条件格式',
    taskId: 'cmdsdv51j002lu48094ov6c6w',
    description: '测试空操作是否能完成条件格式任务',
    errorOperation: '不设置任何条件格式'
  },
  {
    name: '复杂条件格式',
    taskId: 'cmdsdv51o002pu4807mvdbjrc',
    description: '测试错误条件格式是否能完成任务',
    errorOperation: '设置错误的条件格式（不符合任务要求）'
  },
  {
    name: '单元格合并',
    taskId: 'cmdsdv521002yu480vzz54u9x',
    description: '测试空操作是否能完成单元格合并任务',
    errorOperation: '不进行任何单元格合并操作'
  },
  {
    name: '自动换行和强制换行',
    taskId: 'cmdsdv5250032u4801641dppz',
    description: '测试空操作是否能完成换行任务',
    errorOperation: '不设置任何换行格式'
  },
  {
    name: '快速填充公式',
    taskId: 'cmdsdv52a0036u480fcvl158w',
    description: '测试空操作是否能完成公式填充任务',
    errorOperation: '不填充任何公式'
  },
  {
    name: '简单筛选',
    taskId: 'cmdsdv50b001ju480gonoioab',
    description: '测试只开启筛选但不实际筛选是否能完成任务',
    errorOperation: '只开启筛选功能，不设置筛选条件'
  },
  {
    name: '复杂筛选',
    taskId: 'cmdsdv50g001nu4808q43yv1y',
    description: '测试只开启筛选但不实际筛选是否能完成任务',
    errorOperation: '只开启筛选功能，不设置筛选条件'
  },
  {
    name: '数据透视表',
    taskId: 'cmdsdv51t002tu480ai1y58eo',
    description: '测试创建错误的数据透视表是否能完成任务',
    errorOperation: '创建不符合任务要求的数据透视表'
  }
];

async function testTaskValidation(page, task) {
  try {
    console.log(`\n🧪 测试任务: ${task.name}`);
    console.log(`📝 错误操作: ${task.errorOperation}`);

    // 导航到任务页面
    const taskUrl = `http://localhost:5173/task/${task.taskId}`;
    console.log(`🌐 导航到: ${taskUrl}`);
    await page.goto(taskUrl);
    await page.waitForLoadState('networkidle');

    // 检查页面状态
    const currentUrl = page.url();
    const pageTitle = await page.title();
    console.log(`📄 当前URL: ${currentUrl}`);
    console.log(`📝 页面标题: ${pageTitle}`);

    // 等待页面加载完成 - 等待任务标题出现
    await page.waitForSelector('h1', { timeout: 15000 });

    // 等待Excel组件加载完成
    await page.waitForSelector('.univer-container', { timeout: 20000 }).catch(() => {
      console.log('⚠️ Excel组件加载超时，继续测试...');
    });

    // 等待一下让页面完全加载
    await page.waitForTimeout(2000);

    // 不进行任何操作，直接尝试提交任务
    console.log('⏭️ 跳过操作，直接提交任务...');

    // 查找并点击提交按钮
    const submitButton = page.locator('button:has-text("🚀 提交任务")');

    if (await submitButton.count() > 0) {
      console.log('🖱️ 找到提交按钮，点击...');
      await submitButton.first().click();

      // 等待验证结果
      await page.waitForTimeout(5000);

      // 检查页面文本中是否包含验证结果
      const pageText = await page.textContent('body');
      console.log('🔍 检查页面文本中的验证结果...');

      if (pageText.includes('闯关失败') || pageText.includes('❌')) {
        console.log(`✅ 任务正确地被拒绝 - 找到失败消息`);
        return { success: true, message: '正确拒绝了错误操作' };
      } else if (pageText.includes('闯关成功') || pageText.includes('✅') || pageText.includes('任务完成')) {
        console.log(`⚠️ 任务被错误地标记为成功`);
        return { success: false, message: '错误地接受了错误操作' };
      } else {
        console.log('❓ 未找到明确的验证结果');
        // 输出部分页面文本用于调试
        const relevantText = pageText.split('\n').filter(line =>
          line.includes('验证') || line.includes('成功') || line.includes('失败') ||
          line.includes('错误') || line.includes('完成') || line.includes('闯关')
        ).slice(0, 3).join(' | ');
        console.log(`🔍 相关文本: ${relevantText}`);
        return { success: false, message: '未找到验证结果' };
      }
    } else {
      console.log('❓ 未找到提交按钮');
      return { success: false, message: '未找到提交按钮' };
    }

  } catch (error) {
    console.error(`❌ 测试任务 ${task.name} 时发生错误:`, error.message);
    return { success: false, message: `测试错误: ${error.message}` };
  }
}

async function runValidationTests() {
  console.log('🚀 开始第一轮验证测试：错误操作验证');
  console.log('📋 测试目标：确保错误操作无法完成任务\n');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  // 登录
  console.log('🔐 登录测试账户...');
  await page.goto('http://localhost:5173/login');
  await page.waitForLoadState('networkidle');

  // 检查登录页面是否加载
  const loginForm = await page.locator('form').count();
  console.log(`📋 登录表单数量: ${loginForm}`);

  if (loginForm > 0) {
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', '123456');
    await page.click('button[type="submit"]');

    // 等待登录完成，检查是否重定向
    await page.waitForTimeout(3000);
    await page.waitForLoadState('networkidle');

    // 检查是否成功登录
    const currentUrl = page.url();
    console.log(`🌐 登录后URL: ${currentUrl}`);

    if (currentUrl.includes('/login')) {
      console.log('❌ 登录失败，仍在登录页面');
      // 检查是否有错误消息
      const errorMsg = await page.locator('.error, .alert-error, [class*="error"]').first().textContent().catch(() => null);
      if (errorMsg) {
        console.log(`❌ 登录错误: ${errorMsg}`);
      }
      return { total: 0, passed: 0, failed: 0, results: [] };
    } else {
      console.log('✅ 登录成功');
    }
  } else {
    console.log('❌ 未找到登录表单');
    return { total: 0, passed: 0, failed: 0, results: [] };
  }

  const results = [];

  // 测试所有任务
  const keyTasks = TEST_TASKS; // 测试所有任务

  for (const task of keyTasks) {
    const result = await testTaskValidation(page, task);
    results.push({
      task: task.name,
      ...result
    });

    // 短暂等待，避免请求过快
    await page.waitForTimeout(2000);
  }

  // 输出测试结果汇总
  console.log('\n📊 第一轮测试结果汇总:');
  console.log('=' .repeat(60));

  const passedTests = results.filter(r => r.success);
  const failedTests = results.filter(r => !r.success);

  console.log(`✅ 通过测试: ${passedTests.length}/${results.length}`);
  console.log(`❌ 失败测试: ${failedTests.length}/${results.length}`);

  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedTests.forEach(test => {
      console.log(`  - ${test.task}: ${test.message}`);
    });
  }

  if (passedTests.length > 0) {
    console.log('\n✅ 通过的测试:');
    passedTests.forEach(test => {
      console.log(`  - ${test.task}: ${test.message}`);
    });
  }

  await browser.close();

  return {
    total: results.length,
    passed: passedTests.length,
    failed: failedTests.length,
    results
  };
}

// 运行测试
runValidationTests()
  .then(summary => {
    console.log('\n🎯 测试完成!');
    console.log(`总计: ${summary.total}, 通过: ${summary.passed}, 失败: ${summary.failed}`);
    process.exit(summary.failed > 0 ? 1 : 0);
  })
  .catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  });

export { runValidationTests, TEST_TASKS };
