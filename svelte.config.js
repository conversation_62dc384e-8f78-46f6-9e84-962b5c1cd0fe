import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

// 动态导入适配器
let adapter;
if (process.env.ADAPTER_MODE === 'node') {
  adapter = (await import('@sveltejs/adapter-node')).default();
} else if (process.env.NETLIFY || process.env.USE_AUTO) {
  adapter = (await import('@sveltejs/adapter-auto')).default();
} else {
  // 默认回退到 adapter-node
  adapter = (await import('@sveltejs/adapter-node')).default();
}

/** @type {import('@sveltejs/kit').Config} */
const config = {
  preprocess: vitePreprocess(),

  kit: {
    adapter: adapter, // 使用动态选择的适配器
    
    paths: {
      base: process.env.PUBLIC_BASE_URL ? new URL(process.env.PUBLIC_BASE_URL).pathname : '',
      relative: false
    },
    
    env: {
      publicPrefix: 'VITE_PUBLIC_'
    }
  }
};

export default config;