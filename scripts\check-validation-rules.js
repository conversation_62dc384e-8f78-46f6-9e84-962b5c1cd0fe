import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkValidationRules() {
  try {
    // 查询关键任务的验证规则
    const keyTaskNames = ['字体颜色设置', '字体更改', '数字格式', '简单条件格式'];
    
    for (const taskName of keyTaskNames) {
      const task = await prisma.task.findFirst({
        where: { name: taskName }
      });
      
      if (task) {
        console.log(`\n任务: ${task.name}`);
        console.log(`ID: ${task.id}`);
        console.log(`验证规则:`);
        
        if (task.validation) {
          try {
            const validationRule = JSON.parse(task.validation);
            console.log(JSON.stringify(validationRule, null, 2));
          } catch (e) {
            console.log('验证规则解析失败:', task.validation);
          }
        } else {
          console.log('没有验证规则');
        }
        
        console.log('='.repeat(60));
      } else {
        console.log(`未找到任务: ${taskName}`);
      }
    }
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkValidationRules();
