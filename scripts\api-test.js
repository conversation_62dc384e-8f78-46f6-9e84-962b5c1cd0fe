// 直接测试API调用
import { chromium } from '@playwright/test'

async function apiTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🔧 开始API测试...')
    
    // 1. 登录获取session
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 直接调用进度API
    const levelId = 'cmdsdv4yl0007u480wvjzr7iq' // 数据输入关卡ID
    const score = 10 // 数据输入关卡的积分
    
    console.log('📡 调用进度API...')
    const response = await page.evaluate(async (data) => {
      const response = await fetch('/api/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          levelId: data.levelId,
          completed: true,
          score: data.score
        })
      })
      
      const result = await response.json()
      return {
        status: response.status,
        ok: response.ok,
        data: result
      }
    }, { levelId, score })
    
    console.log('📊 API响应:', response)
    
    if (response.ok) {
      console.log('✅ API调用成功')
      
      // 3. 刷新页面检查经验值
      await page.reload()
      await page.waitForLoadState('networkidle')
      
      const navText = await page.textContent('nav')
      const updatedScore = navText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
      console.log('📊 更新后经验值:', updatedScore)
      
      if (parseInt(updatedScore) > 0) {
        console.log('🎉 经验值成功更新！')
      } else {
        console.log('❌ 经验值仍为0')
      }
    } else {
      console.log('❌ API调用失败:', response.data)
    }
    
    // 4. 检查数据库状态
    console.log('🔍 检查数据库状态...')
    
    console.log('🎉 API测试完成！浏览器将保持打开30秒...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ API测试失败:', error)
    await page.screenshot({ path: 'api-test-error.png' })
  } finally {
    await browser.close()
  }
}

apiTest()
