import { chromium } from '@playwright/test'

async function testDashboard() {
  const browser = await chromium.launch({ headless: false })
  const page = await browser.newPage()
  
  try {
    console.log('Navigating to dashboard page...')
    await page.goto('http://localhost:5173/dashboard')
    
    console.log('Current URL:', page.url())
    await page.screenshot({ path: 'dashboard-direct.png' })
    
    const pageTitle = await page.title()
    console.log('Page title:', pageTitle)
    
  } catch (error) {
    console.error('Test failed:', error)
  } finally {
    await browser.close()
  }
}

testDashboard()
