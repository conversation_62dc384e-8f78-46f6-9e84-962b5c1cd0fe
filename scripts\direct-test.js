// 直接测试任务页面
import { chromium } from '@playwright/test'

async function directTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  })
  
  const page = await browser.newPage()
  
  try {
    console.log('🎯 开始直接测试任务页面...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 检查初始经验值
    const initialNavText = await page.textContent('nav')
    const initialScore = initialNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 初始经验值:', initialScore)
    
    // 3. 直接访问数据输入任务页面
    const taskId = 'cmdsdv4yp0009u480e9dd3pw8' // 从数据库查询得到的任务ID
    await page.goto(`http://localhost:5173/task/${taskId}`)
    await page.waitForLoadState('networkidle')
    console.log('✅ 直接访问数据输入任务页面')
    
    // 4. 等待页面加载
    await page.waitForTimeout(3000)
    
    // 5. 检查页面内容
    const pageTitle = await page.textContent('h1, h2, h3')
    console.log('📋 页面标题:', pageTitle)
    
    // 6. 查找所有按钮
    const allButtons = await page.locator('button').allTextContents()
    console.log('🔘 页面所有按钮:', allButtons)
    
    // 7. 查找完成相关的按钮
    const completeButtons = [
      'button:has-text("完成任务")',
      'button:has-text("提交答案")', 
      'button:has-text("完成")',
      'button:has-text("提交")',
      'button:has-text("确认完成")',
      'button[type="submit"]'
    ]
    
    let buttonFound = false
    for (const selector of completeButtons) {
      const button = page.locator(selector).first()
      if (await button.isVisible()) {
        console.log(`✅ 找到完成按钮: ${selector}`)
        
        // 监听网络请求
        page.on('response', response => {
          if (response.url().includes('/api/progress')) {
            console.log('📡 进度API响应:', response.status(), response.url())
          }
        })
        
        await button.click()
        console.log('✅ 点击完成按钮')
        buttonFound = true
        
        // 等待API响应
        await page.waitForTimeout(5000)
        break
      }
    }
    
    if (!buttonFound) {
      console.log('❌ 未找到完成按钮')
      
      // 尝试查找任何可能的提交按钮
      const anySubmitButton = page.locator('button, input[type="submit"], a').filter({ hasText: /完成|提交|确认|下一步/ })
      const submitButtonCount = await anySubmitButton.count()
      console.log(`🔍 找到 ${submitButtonCount} 个可能的提交元素`)
      
      if (submitButtonCount > 0) {
        const firstSubmitButton = anySubmitButton.first()
        const buttonText = await firstSubmitButton.textContent()
        console.log('🎯 尝试点击第一个提交元素:', buttonText)
        
        page.on('response', response => {
          if (response.url().includes('/api/progress')) {
            console.log('📡 进度API响应:', response.status(), response.url())
          }
        })
        
        await firstSubmitButton.click()
        await page.waitForTimeout(5000)
      }
    }
    
    // 8. 检查是否有成功提示
    const pageContent = await page.textContent('body')
    if (pageContent.includes('恭喜') || pageContent.includes('完成') || pageContent.includes('成功')) {
      console.log('✅ 看到成功提示')
    } else {
      console.log('⚠️ 未看到明显的成功提示')
    }
    
    // 9. 返回dashboard检查经验值
    await page.goto('http://localhost:5173/dashboard')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    const updatedNavText = await page.textContent('nav')
    const updatedScore = updatedNavText.match(/⭐\s*(\d+)\s*EXP/)?.[1] || '0'
    console.log('📊 更新后经验值:', updatedScore)
    
    if (parseInt(updatedScore) > parseInt(initialScore)) {
      console.log('🎉 经验值成功更新！')
    } else {
      console.log('❌ 经验值未更新')
    }
    
    console.log('🎉 测试完成！浏览器将保持打开30秒供检查...')
    await page.waitForTimeout(30000)
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    await page.screenshot({ path: 'direct-test-error.png' })
  } finally {
    await browser.close()
  }
}

directTest()
