import { chromium } from 'playwright';

async function simpleValidationTest() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔍 开始简单验证测试...')
    
    // 1. 登录
    await page.goto('http://localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 测试字体颜色设置任务
    console.log('\n📝 测试字体颜色设置任务...')
    await page.goto('http://localhost:5174/task/cmdsdv4z7000lu480dduqvycf')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 第一次测试：不做任何操作，直接提交')
    
    // 直接提交（不做任何操作）
    let submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
    await submitButton.click()
    await page.waitForTimeout(8000)
    
    let pageContent = await page.content()
    let hasError1 = pageContent.includes('验证失败') || pageContent.includes('不正确') || pageContent.includes('错误')
    
    console.log(`  结果：${hasError1 ? '✅ 正确地验证失败' : '❌ 错误地通过验证'}`)
    
    // 刷新页面准备第二次测试
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 第二次测试：手动操作后提交')
    console.log('  请在浏览器中手动：')
    console.log('    1. 选择单元格A1')
    console.log('    2. 设置字体颜色为红色')
    console.log('    3. 设置背景颜色为黄色')
    console.log('    4. 然后点击提交按钮')
    console.log('  等待60秒供手动操作...')
    
    // 等待手动操作
    await page.waitForTimeout(60000)
    
    // 检查是否有验证结果
    pageContent = await page.content()
    const hasSuccess = pageContent.includes('验证通过') || pageContent.includes('任务完成') || pageContent.includes('恭喜')
    const hasError2 = pageContent.includes('验证失败') || pageContent.includes('不正确') || pageContent.includes('错误')
    
    console.log(`  手动操作结果：成功=${hasSuccess}, 失败=${hasError2}`)
    
    // 3. 测试数字格式任务
    console.log('\n📝 测试数字格式任务...')
    await page.goto('http://localhost:5174/task/cmdsdv4zc000pu480ozbjqgbm')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 第一次测试：不做任何操作，直接提交')
    
    submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
    await submitButton.click()
    await page.waitForTimeout(8000)
    
    pageContent = await page.content()
    const hasError3 = pageContent.includes('验证失败') || pageContent.includes('不正确') || pageContent.includes('错误')
    
    console.log(`  结果：${hasError3 ? '✅ 正确地验证失败' : '❌ 错误地通过验证'}`)
    
    // 刷新页面准备第二次测试
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 第二次测试：手动操作后提交')
    console.log('  请在浏览器中手动：')
    console.log('    1. 选择单元格A1')
    console.log('    2. 输入数字（如1000）')
    console.log('    3. 设置为货币格式')
    console.log('    4. 然后点击提交按钮')
    console.log('  等待60秒供手动操作...')
    
    // 等待手动操作
    await page.waitForTimeout(60000)
    
    // 检查是否有验证结果
    pageContent = await page.content()
    const hasSuccess2 = pageContent.includes('验证通过') || pageContent.includes('任务完成') || pageContent.includes('恭喜')
    const hasError4 = pageContent.includes('验证失败') || pageContent.includes('不正确') || pageContent.includes('错误')
    
    console.log(`  手动操作结果：成功=${hasSuccess2}, 失败=${hasError4}`)
    
    // 总结
    console.log('\n📊 测试总结:')
    console.log('='.repeat(50))
    console.log(`字体颜色设置 - 空操作: ${hasError1 ? '✅ 正确失败' : '❌ 错误通过'}`)
    console.log(`数字格式 - 空操作: ${hasError3 ? '✅ 正确失败' : '❌ 错误通过'}`)
    
    const emptyOperationsPassed = (hasError1 ? 1 : 0) + (hasError3 ? 1 : 0)
    console.log(`\n空操作验证: ${emptyOperationsPassed}/2 个任务正确地验证失败`)
    
    if (emptyOperationsPassed === 2) {
      console.log('🎉 验证逻辑修复成功！所有空操作都正确地被拒绝了')
    } else {
      console.log('⚠️ 还有验证逻辑需要进一步修复')
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    console.log('\n浏览器将保持打开30秒供最终检查...')
    await page.waitForTimeout(30000)
    await browser.close()
  }
}

simpleValidationTest()
