// 测试认证功能的脚本
const fetch = require('node-fetch');

const BASE_URL = 'http://115.190.155.92:4000';

async function testAuth() {
  console.log('开始测试认证功能...\n');

  try {
    // 1. 测试首页访问
    console.log('1. 测试首页访问...');
    const homeResponse = await fetch(`${BASE_URL}/`);
    console.log(`首页状态码: ${homeResponse.status}`);
    
    // 2. 测试登录页面访问
    console.log('\n2. 测试登录页面访问...');
    const loginResponse = await fetch(`${BASE_URL}/login`);
    console.log(`登录页面状态码: ${loginResponse.status}`);
    
    // 3. 测试dashboard重定向（未登录状态）
    console.log('\n3. 测试dashboard重定向（未登录状态）...');
    const dashboardResponse = await fetch(`${BASE_URL}/dashboard`, {
      redirect: 'manual'
    });
    console.log(`Dashboard状态码: ${dashboardResponse.status}`);
    if (dashboardResponse.status === 302) {
      console.log(`重定向到: ${dashboardResponse.headers.get('location')}`);
    }
    
    // 4. 测试退出登录API
    console.log('\n4. 测试退出登录API...');
    const logoutResponse = await fetch(`${BASE_URL}/api/logout`, {
      method: 'POST',
      redirect: 'manual'
    });
    console.log(`退出登录状态码: ${logoutResponse.status}`);
    if (logoutResponse.status === 302) {
      console.log(`重定向到: ${logoutResponse.headers.get('location')}`);
    }
    
    console.log('\n测试完成！');
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAuth();
}

module.exports = { testAuth };
