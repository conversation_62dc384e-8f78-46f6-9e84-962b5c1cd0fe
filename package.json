{"name": "learn-excel-svelte", "private": true, "version": "0.0.1", "type": "module", "scripts": {"lint": "eslint src/", "postinstall": "prisma generate", "dev": "vite dev", "build": "vite build", "build:prod": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "start": "node server.js", "cluster": "node cluster.js"}, "prisma": {"seed": "npx tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@auth/sveltekit": "^1.10.0", "@prisma/client": "^6.11.1", "@sveltejs/adapter-node": "^5.2.13", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@univerjs-pro/sheets-chart": "^0.10.2", "@univerjs-pro/sheets-chart-ui": "^0.10.2", "@univerjs-pro/sheets-pivot": "^0.10.2", "@univerjs-pro/sheets-pivot-ui": "^0.10.2", "@univerjs/core": "^0.10.2", "@univerjs/data-validation": "^0.10.2", "@univerjs/design": "^0.10.2", "@univerjs/docs": "^0.10.2", "@univerjs/docs-ui": "^0.10.2", "@univerjs/engine-formula": "^0.10.2", "@univerjs/engine-render": "^0.10.2", "@univerjs/sheets": "^0.10.2", "@univerjs/sheets-conditional-formatting": "^0.10.2", "@univerjs/sheets-conditional-formatting-ui": "^0.10.2", "@univerjs/sheets-data-validation": "^0.10.2", "@univerjs/sheets-data-validation-ui": "^0.10.2", "@univerjs/sheets-filter": "^0.10.2", "@univerjs/sheets-filter-ui": "^0.10.2", "@univerjs/sheets-formula": "^0.10.2", "@univerjs/sheets-formula-ui": "^0.10.2", "@univerjs/sheets-numfmt": "^0.10.2", "@univerjs/sheets-numfmt-ui": "^0.10.2", "@univerjs/sheets-sort": "^0.10.2", "@univerjs/sheets-sort-ui": "^0.10.2", "@univerjs/sheets-table": "^0.10.2", "@univerjs/sheets-table-ui": "^0.10.2", "@univerjs/sheets-ui": "^0.10.2", "@univerjs/ui": "^0.10.2", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "prisma": "^6.11.0", "rxjs": "^7.8.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@playwright/test": "^1.54.1", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@types/node": "^20.19.4", "eslint": "^9.32.0", "eslint-plugin-svelte": "^3.11.0", "playwright": "^1.54.1", "postcss": "^8.5.6", "svelte": "5.37.3", "svelte-check": "^4.3.1", "typescript": "^5.0.0", "vite": "^7.0.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}