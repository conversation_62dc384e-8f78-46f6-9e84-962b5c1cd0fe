import { chromium } from 'playwright';

async function debugValidation() {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 监听控制台输出
  page.on('console', msg => {
    console.log(`🖥️ [${msg.type()}] ${msg.text()}`);
  });
  
  try {
    console.log('🔍 开始调试验证过程...')
    
    // 1. 登录
    await page.goto('http://localhost:5173/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', '123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/dashboard')
    console.log('✅ 登录成功')
    
    // 2. 测试字体更改任务
    console.log('\n📝 测试字体更改任务...')
    await page.goto('http://localhost:5173/task/cmdsdv4z1000hu4804evogf7v')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(8000)
    
    console.log('  🔸 不做任何操作，直接提交')
    
    // 直接提交（不做任何操作）
    let submitButton = page.locator('button').filter({ hasText: /提交|submit/i }).first()
    await submitButton.click()
    
    // 等待验证完成
    await page.waitForTimeout(5000)
    
    // 检查页面内容
    let pageContent = await page.content()
    console.log('\n📄 页面内容片段:')
    
    // 查找验证相关的内容
    const validationMessages = await page.locator('div').filter({ hasText: /验证|失败|成功|错误|通过/ }).allTextContents()
    console.log('🔍 验证消息:', validationMessages)
    
    // 查找所有可能的错误或成功消息
    const allMessages = await page.locator('div, span, p').filter({ hasText: /验证|失败|成功|错误|通过|未设置|缺少|不正确/ }).allTextContents()
    console.log('📋 所有相关消息:', allMessages)
    
    // 检查是否有特定的验证结果显示
    const hasValidationResult = await page.locator('[class*="validation"], [class*="result"], [class*="message"]').count()
    console.log('🎯 验证结果元素数量:', hasValidationResult)
    
    // 等待更长时间看是否有延迟的消息
    console.log('\n⏳ 等待10秒查看是否有延迟消息...')
    await page.waitForTimeout(10000)
    
    // 再次检查
    const finalMessages = await page.locator('div, span, p').filter({ hasText: /验证|失败|成功|错误|通过|未设置|缺少|不正确/ }).allTextContents()
    console.log('📋 最终消息:', finalMessages)
    
    console.log('\n📊 调试完成')
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error)
  } finally {
    // 不关闭浏览器，让用户可以手动检查
    console.log('\n🔍 浏览器保持打开状态，请手动检查页面')
    // await browser.close()
  }
}

debugValidation()
